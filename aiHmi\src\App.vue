<template>
  <f7-app :params="f7params">
    <f7-view main>
      <router-view />
    </f7-view>
  </f7-app>
</template>

<script>
export default {
  name: 'App',
  data() {
    return {
      f7params: {
        name: 'AI-HMI',
        theme: 'auto',
        colors: {
          primary: '#00d4ff',
        },
        routes: [
          {
            path: '/',
            component: () => import('./views/Home.vue'),
          },
        ],
      }
    }
  }
}
</script>
