import { createApp } from 'vue'
import App from './App.vue'
import router from './router'

// Framework7 Vue plugin
import Framework7 from 'framework7/lite-bundle'
import Framework7Vue, { registerComponents } from 'framework7-vue/bundle'

// Framework7 Styles
import 'framework7/css/bundle'

// Tailwind CSS
import './style.css'

// Configure Framework7
Framework7.use(Framework7Vue)

const app = createApp(App)

// Register Framework7 Vue components
registerComponents(app)

app.use(router)
app.mount('#app')
