<template>
  <f7-page class="bg-gradient-to-br from-tech-dark to-tech-gray min-h-screen">
    <WheelMenu @menu-selected="onMenuSelected" />
    
    <!-- 状态显示 -->
    <div class="absolute top-4 left-4 z-20">
      <div class="bg-black bg-opacity-50 rounded-lg p-4 text-white">
        <h3 class="text-lg font-bold mb-2 neon-text">AI-HMI 控制中心</h3>
        <p class="text-sm opacity-75">点击转盘菜单项进行交互</p>
        <div v-if="selectedItem" class="mt-2 p-2 bg-tech-blue bg-opacity-20 rounded">
          <p class="text-sm">已选择: {{ selectedItem.label }}</p>
        </div>
      </div>
    </div>
    
    <!-- 右上角装饰 -->
    <div class="absolute top-4 right-4 z-20">
      <div class="w-16 h-16 border-2 border-tech-blue rounded-full animate-spin-slow opacity-50"></div>
    </div>
    
    <!-- 底部装饰线 -->
    <div class="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-tech-blue to-transparent opacity-50"></div>
  </f7-page>
</template>

<script>
import WheelMenu from '../components/WheelMenu.vue'

export default {
  name: 'Home',
  components: {
    WheelMenu
  },
  data() {
    return {
      selectedItem: null
    }
  },
  methods: {
    onMenuSelected(item) {
      this.selectedItem = item
      console.log('主页接收到选择事件:', item)
      
      // 这里可以添加路由跳转或其他逻辑
      // this.$router.push(`/${item.route}`)
    }
  }
}
</script>
