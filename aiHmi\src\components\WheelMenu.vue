<template>
  <div class="wheel-container">
    <!-- p5.js 画布将在这里渲染 -->
    <div ref="p5Container" class="relative w-full h-full"></div>
    
    <!-- 中心机器人 -->
    <div class="robot-center">
      <div class="relative">
        <img
          src="https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=200&h=200&fit=crop&crop=center"
          alt="AI Robot"
          class="w-32 h-32 rounded-full animate-float tech-glow"
        />
        <!-- 旋转光环 -->
        <div class="absolute inset-0 w-32 h-32 border-2 border-tech-blue rounded-full animate-spin-slow opacity-30"></div>
        <div class="absolute inset-2 w-28 h-28 border border-neon-purple rounded-full animate-spin-slow opacity-50" style="animation-direction: reverse;"></div>
      </div>
      <div class="text-center mt-4">
        <h2 class="text-2xl font-bold neon-text">AI-HMI</h2>
        <p class="text-sm text-tech-blue opacity-75 mt-1">智能交互系统</p>
      </div>
    </div>
  </div>
</template>

<script>
import p5 from 'p5'

export default {
  name: 'WheelMenu',
  data() {
    return {
      p5Instance: null,
      menuItems: [
        { icon: 'fas fa-car', label: '智车导航', color: '#00d4ff', bgColor: 'rgba(0, 212, 255, 0.2)' },
        { icon: 'fas fa-utensils', label: '美食导航', color: '#ff6b6b', bgColor: 'rgba(255, 107, 107, 0.2)' },
        { icon: 'fas fa-map', label: '地图导航', color: '#4ecdc4', bgColor: 'rgba(78, 205, 196, 0.2)' },
        { icon: 'fas fa-music', label: '听我说话', color: '#45b7d1', bgColor: 'rgba(69, 183, 209, 0.2)' },
        { icon: 'fas fa-graduation-cap', label: '亲子学堂', color: '#96ceb4', bgColor: 'rgba(150, 206, 180, 0.2)' },
        { icon: 'fas fa-cog', label: '专属配置', color: '#feca57', bgColor: 'rgba(254, 202, 87, 0.2)' },
        { icon: 'fas fa-shield-alt', label: '专属配置', color: '#ff9ff3', bgColor: 'rgba(255, 159, 243, 0.2)' },
        { icon: 'fas fa-comments', label: '听我说话', color: '#54a0ff', bgColor: 'rgba(84, 160, 255, 0.2)' }
      ],
      currentRotation: 0,
      targetRotation: 0,
      isAnimating: false,
      hoveredIndex: -1,
      velocity: 0,
      damping: 0.95,
      springStrength: 0.15,
      isDragging: false,
      lastMouseAngle: 0,
      dragStartAngle: 0
    }
  },
  mounted() {
    this.initP5()
  },
  beforeUnmount() {
    if (this.p5Instance) {
      this.p5Instance.remove()
    }
  },
  methods: {
    initP5() {
      const sketch = (p) => {
        const radius = 300
        const centerX = p.windowWidth / 2
        const centerY = p.windowHeight / 2
        const itemRadius = 60
        
        p.setup = () => {
          p.createCanvas(p.windowWidth, p.windowHeight)
          p.angleMode(p.DEGREES)
        }
        
        p.draw = () => {
          p.clear()

          // 绘制背景光环
          p.push()
          p.translate(centerX, centerY)
          p.noFill()
          p.stroke(0, 212, 255, 30)
          p.strokeWeight(1)
          p.circle(0, 0, radius * 2.2)
          p.stroke(139, 92, 246, 20)
          p.circle(0, 0, radius * 2.4)
          p.pop()

          // 绘制转盘内容
          p.push()
          p.translate(centerX, centerY)
          p.rotate(this.currentRotation)

          // 绘制连接线和光效
          for (let i = 0; i < this.menuItems.length; i++) {
            const angle = (360 / this.menuItems.length) * i
            const x = p.cos(angle) * radius
            const y = p.sin(angle) * radius

            // 连接线
            p.stroke(0, 212, 255, 60)
            p.strokeWeight(1)
            p.line(0, 0, x, y)

            // 光效线
            p.stroke(0, 212, 255, 20)
            p.strokeWeight(3)
            p.line(x * 0.7, y * 0.7, x * 1.1, y * 1.1)
          }

          // 绘制菜单项
          for (let i = 0; i < this.menuItems.length; i++) {
            const angle = (360 / this.menuItems.length) * i
            const x = p.cos(angle) * radius
            const y = p.sin(angle) * radius
            const item = this.menuItems[i]
            const isHovered = this.hoveredIndex === i
            const scale = isHovered ? 1.1 : 1

            p.push()
            p.translate(x, y)
            p.scale(scale)

            // 绘制发光背景
            if (isHovered) {
              p.fill(p.red(item.color), p.green(item.color), p.blue(item.color), 30)
              p.noStroke()
              p.circle(0, 0, itemRadius * 1.5)
            }

            // 绘制菜单项背景
            p.fill(30, 41, 59, 220)
            p.stroke(p.red(item.color), p.green(item.color), p.blue(item.color), 180)
            p.strokeWeight(2)
            p.circle(0, 0, itemRadius)

            // 绘制内圈
            p.fill(p.red(item.color), p.green(item.color), p.blue(item.color), 100)
            p.noStroke()
            p.circle(0, 0, itemRadius * 0.7)

            // 绘制图标占位符（简化的几何形状）
            p.fill(255, 255, 255, 200)
            p.noStroke()
            this.drawIconShape(p, item.icon, itemRadius * 0.3)

            p.pop()
          }

          p.pop()
          
          // 更新旋转动画
          if (this.isAnimating) {
            const diff = this.targetRotation - this.currentRotation
            if (Math.abs(diff) > 1) {
              this.currentRotation += diff * 0.1 // 缓动效果
            } else {
              this.currentRotation = this.targetRotation
              this.isAnimating = false
            }
          }
        }
        
        p.mouseMoved = () => {
          const mouseX = p.mouseX - centerX
          const mouseY = p.mouseY - centerY
          const distance = p.dist(0, 0, mouseX, mouseY)

          this.hoveredIndex = -1

          if (distance > radius - itemRadius && distance < radius + itemRadius) {
            // 计算鼠标悬停的角度
            let angle = p.atan2(mouseY, mouseX)
            if (angle < 0) angle += 360

            // 调整角度考虑当前旋转
            angle = (angle - this.currentRotation + 360) % 360

            // 确定悬停的菜单项
            const itemAngle = 360 / this.menuItems.length
            this.hoveredIndex = Math.round(angle / itemAngle) % this.menuItems.length
          }
        }

        p.mousePressed = () => {
          const mouseX = p.mouseX - centerX
          const mouseY = p.mouseY - centerY
          const distance = p.dist(0, 0, mouseX, mouseY)

          if (distance > radius - itemRadius && distance < radius + itemRadius) {
            // 计算点击的角度
            let angle = p.atan2(mouseY, mouseX)
            if (angle < 0) angle += 360

            // 调整角度考虑当前旋转
            angle = (angle - this.currentRotation + 360) % 360

            // 确定点击的菜单项
            const itemAngle = 360 / this.menuItems.length
            const clickedIndex = Math.round(angle / itemAngle) % this.menuItems.length

            this.selectMenuItem(clickedIndex)
          }
        }
        
        p.windowResized = () => {
          p.resizeCanvas(p.windowWidth, p.windowHeight)
        }
      }
      
      this.p5Instance = new p5(sketch, this.$refs.p5Container)
    },
    
    selectMenuItem(index) {
      // 计算需要旋转的角度，使选中的项目移动到顶部
      const itemAngle = 360 / this.menuItems.length
      const targetAngle = -index * itemAngle

      this.targetRotation = targetAngle
      this.isAnimating = true

      // 触发选择事件
      this.$emit('menu-selected', this.menuItems[index])

      console.log('选中菜单项:', this.menuItems[index].label)
    },

    drawIconShape(p, iconClass, size) {
      // 根据图标类型绘制简化的几何形状
      p.push()

      switch(iconClass) {
        case 'fas fa-car':
          // 汽车图标 - 矩形
          p.rect(-size/2, -size/3, size, size/1.5, 3)
          break
        case 'fas fa-utensils':
          // 餐具图标 - 叉子形状
          p.line(0, -size/2, 0, size/2)
          p.line(-size/4, -size/3, size/4, -size/3)
          break
        case 'fas fa-map':
          // 地图图标 - 菱形
          p.beginShape()
          p.vertex(0, -size/2)
          p.vertex(size/2, 0)
          p.vertex(0, size/2)
          p.vertex(-size/2, 0)
          p.endShape(p.CLOSE)
          break
        case 'fas fa-music':
          // 音乐图标 - 音符
          p.circle(size/4, size/4, size/3)
          p.line(size/4 + size/6, size/4, size/4 + size/6, -size/2)
          break
        case 'fas fa-graduation-cap':
          // 学士帽图标 - 三角形
          p.triangle(0, -size/2, -size/2, size/3, size/2, size/3)
          break
        case 'fas fa-cog':
          // 齿轮图标 - 八角形
          p.beginShape()
          for (let i = 0; i < 8; i++) {
            const angle = (360 / 8) * i
            const r = i % 2 === 0 ? size/2 : size/3
            const x = p.cos(p.radians(angle)) * r
            const y = p.sin(p.radians(angle)) * r
            p.vertex(x, y)
          }
          p.endShape(p.CLOSE)
          break
        case 'fas fa-shield-alt':
          // 盾牌图标 - 盾形
          p.beginShape()
          p.vertex(0, -size/2)
          p.vertex(size/3, -size/3)
          p.vertex(size/3, size/4)
          p.vertex(0, size/2)
          p.vertex(-size/3, size/4)
          p.vertex(-size/3, -size/3)
          p.endShape(p.CLOSE)
          break
        case 'fas fa-comments':
          // 对话图标 - 圆角矩形
          p.rect(-size/2, -size/3, size, size/1.5, size/4)
          break
        default:
          // 默认图标 - 圆形
          p.circle(0, 0, size)
      }

      p.pop()
    }
  }
}
</script>
