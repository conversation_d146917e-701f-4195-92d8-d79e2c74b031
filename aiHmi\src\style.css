@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义科技感样式 */
:root {
  --tech-primary: #00d4ff;
  --tech-secondary: #6366f1;
  --tech-dark: #0f172a;
  --tech-gray: #1e293b;
  --neon-blue: #00ffff;
  --neon-purple: #8b5cf6;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, var(--tech-dark) 0%, var(--tech-gray) 100%);
  overflow: hidden;
}

/* 科技感发光效果 */
.tech-glow {
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.neon-text {
  text-shadow: 0 0 10px var(--neon-blue);
  color: var(--neon-blue);
}

/* 转盘容器样式 */
.wheel-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: radial-gradient(circle at center, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
}

/* p5.js画布样式 */
#wheel-canvas {
  border-radius: 50%;
  box-shadow: 0 0 50px rgba(0, 212, 255, 0.3);
}

/* 中心机器人容器 */
.robot-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  pointer-events: none;
}

/* 菜单项样式 */
.menu-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.menu-item:hover {
  transform: scale(1.1);
  filter: brightness(1.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wheel-container {
    padding: 20px;
  }
}

/* 动画关键帧 */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse-glow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
  }
  50% { 
    box-shadow: 0 0 40px rgba(0, 212, 255, 0.8);
  }
}
